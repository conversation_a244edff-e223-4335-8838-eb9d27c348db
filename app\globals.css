@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

:root {
  --background: #ffffff;
  --foreground: #1E3A5F;

  /* SAJIDA Foundation Brand Colors */
  --sajida-deep-blue: #1E3A5F;
  --sajida-bright-yellow: #FFD200;
  --sajida-white: #FFFFFF;
  --sajida-light-blue: #EFF4FA;
  --sajida-overlay: rgba(30, 58, 95, 0.7);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* SAJIDA Brand Colors */
  --color-sajida-deep-blue: var(--sajida-deep-blue);
  --color-sajida-bright-yellow: var(--sajida-bright-yellow);
  --color-sajida-white: var(--sajida-white);
  --color-sajida-light-blue: var(--sajida-light-blue);
  --color-sajida-overlay: var(--sajida-overlay);

  /* Typography */
  --font-script: 'Dancing Script', cursive;
  --font-body: 'Open Sans', sans-serif;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Open Sans', sans-serif;
  line-height: 1.6;
}

/* Custom Typography Classes */
.font-script {
  font-family: 'Dancing Script', cursive;
}

.font-body {
  font-family: 'Open Sans', sans-serif;
}

/* Button Styles */
.btn-primary {
  @apply bg-sajida-white text-sajida-deep-blue px-6 py-3 rounded-full font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg;
}

.btn-secondary {
  @apply bg-sajida-bright-yellow text-sajida-deep-blue px-6 py-3 rounded-full font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg;
}

/* Overlay Gradient */
.hero-overlay {
  background: linear-gradient(135deg, rgba(30, 58, 95, 0.8) 0%, rgba(30, 58, 95, 0.4) 100%);
}

/* Section Spacing */
.section-padding {
  @apply py-16 px-4 sm:px-6 lg:px-8;
}
