<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="neuroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F3E5F5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E1BEE7;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="400" fill="url(#neuroGradient)"/>
  <!-- Brain/neuroscience representation -->
  <circle cx="300" cy="200" r="100" fill="#9C27B0" opacity="0.3"/>
  <path d="M250 150 Q300 100 350 150 Q400 200 350 250 Q300 300 250 250 Q200 200 250 150" fill="#7B1FA2" opacity="0.4"/>
  <!-- Neural network lines -->
  <line x1="200" y1="100" x2="250" y2="150" stroke="#6A1B9A" stroke-width="2" opacity="0.6"/>
  <line x1="400" y1="100" x2="350" y2="150" stroke="#6A1B9A" stroke-width="2" opacity="0.6"/>
  <line x1="200" y1="300" x2="250" y2="250" stroke="#6A1B9A" stroke-width="2" opacity="0.6"/>
  <line x1="400" y1="300" x2="350" y2="250" stroke="#6A1B9A" stroke-width="2" opacity="0.6"/>
  <!-- Nodes -->
  <circle cx="200" cy="100" r="8" fill="#4A148C"/>
  <circle cx="400" cy="100" r="8" fill="#4A148C"/>
  <circle cx="200" cy="300" r="8" fill="#4A148C"/>
  <circle cx="400" cy="300" r="8" fill="#4A148C"/>
</svg>
