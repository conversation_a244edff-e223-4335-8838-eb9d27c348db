<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2E7D32;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#66BB6A;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="1920" height="1080" fill="url(#heroGradient)"/>
  <!-- Abstract shapes representing nature/healthcare -->
  <circle cx="1600" cy="200" r="150" fill="#81C784" opacity="0.3"/>
  <circle cx="300" cy="800" r="100" fill="#A5D6A7" opacity="0.4"/>
  <path d="M800 300 Q900 200 1000 300 Q1100 400 1200 300" stroke="#C8E6C9" stroke-width="3" fill="none" opacity="0.5"/>
  <path d="M400 600 Q500 500 600 600 Q700 700 800 600" stroke="#E8F5E8" stroke-width="2" fill="none" opacity="0.6"/>
</svg>
