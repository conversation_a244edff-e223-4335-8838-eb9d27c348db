<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="reportGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF3E0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFE0B2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="400" fill="url(#reportGradient)"/>
  <!-- Document representation -->
  <rect x="150" y="80" width="300" height="240" rx="10" fill="#FFFFFF" stroke="#FF9800" stroke-width="2"/>
  <!-- Chart/graph elements -->
  <rect x="180" y="120" width="40" height="80" fill="#FF9800" opacity="0.7"/>
  <rect x="240" y="140" width="40" height="60" fill="#FF9800" opacity="0.7"/>
  <rect x="300" y="110" width="40" height="90" fill="#FF9800" opacity="0.7"/>
  <rect x="360" y="130" width="40" height="70" fill="#FF9800" opacity="0.7"/>
  <!-- Text lines -->
  <rect x="180" y="240" width="240" height="8" rx="4" fill="#E0E0E0"/>
  <rect x="180" y="260" width="200" height="8" rx="4" fill="#E0E0E0"/>
  <rect x="180" y="280" width="220" height="8" rx="4" fill="#E0E0E0"/>
</svg>
