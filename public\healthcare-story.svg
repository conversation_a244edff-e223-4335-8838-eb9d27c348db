<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="healthcareGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E3F2FD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BBDEFB;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="400" fill="url(#healthcareGradient)"/>
  <!-- Healthcare symbols -->
  <circle cx="300" cy="200" r="80" fill="#2196F3" opacity="0.2"/>
  <rect x="290" y="160" width="20" height="80" fill="#1976D2"/>
  <rect x="260" y="190" width="80" height="20" fill="#1976D2"/>
  <!-- Abstract figures representing people -->
  <circle cx="150" cy="120" r="25" fill="#1565C0" opacity="0.6"/>
  <rect x="135" y="145" width="30" height="60" rx="15" fill="#1565C0" opacity="0.6"/>
  <circle cx="450" cy="120" r="25" fill="#1565C0" opacity="0.6"/>
  <rect x="435" y="145" width="30" height="60" rx="15" fill="#1565C0" opacity="0.6"/>
</svg>
