'use client';

import Image from "next/image";
import { useState } from "react";

// Header Component
function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="absolute top-0 left-0 right-0 z-50 bg-transparent">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <div className="flex items-center">
            <Image
              src="/sajida-logo.svg"
              alt="SAJIDA Foundation"
              width={200}
              height={60}
              className="h-12 w-auto"
            />
          </div>

          {/* Navigation Menu - Desktop */}
          <div className="hidden md:flex items-center space-x-8">
            <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
              We Are SAJIDA
            </a>
            <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
              SAJIDA's Programmes
            </a>
            <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
              Knowledge Hub
            </a>
            <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
              Engage With Us
            </a>
          </div>

          {/* Icons and Mobile Menu Button */}
          <div className="flex items-center space-x-4">
            <button className="text-white hover:text-yellow-400 transition-colors">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
            <button className="text-white hover:text-yellow-400 transition-colors">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </button>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden text-white hover:text-yellow-400 transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-blue-900 bg-opacity-95 rounded-lg mt-2 p-4">
            <div className="flex flex-col space-y-4">
              <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
                We Are SAJIDA
              </a>
              <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
                SAJIDA's Programmes
              </a>
              <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
                Knowledge Hub
              </a>
              <a href="#" className="text-white hover:text-yellow-400 transition-colors font-medium">
                Engage With Us
              </a>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}

export default function Home() {
  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-start bg-cover bg-center bg-no-repeat"
               style={{backgroundImage: "url('/hero-bg.svg')"}}>
        <div className="absolute inset-0 hero-overlay"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl">
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-script text-white mb-4 leading-tight">
              Health, Happiness
            </h1>
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-script text-yellow-400 mb-8 leading-tight">
              & Dignity for All
            </h1>
            <p className="text-lg sm:text-xl text-white opacity-90 max-w-2xl leading-relaxed">
              Empowering communities through innovative healthcare solutions, sustainable development, and unwavering commitment to human dignity.
            </p>
          </div>
        </div>
      </section>

      {/* Our Approach Section */}
      <section className="section-padding bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Left Column - Approaches */}
            <div>
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-script text-blue-900 mb-8 lg:mb-12">
                Our Approach
              </h2>

              <div className="space-y-8">
                {/* Approach 1 */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <svg className="w-8 h-8 text-blue-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-blue-900 mb-2">Empowering Communities</h3>
                    <p className="text-gray-600 mb-3">Building sustainable healthcare solutions through community engagement and local capacity building.</p>
                    <a href="#" className="text-blue-900 hover:text-yellow-500 font-medium">See Details →</a>
                  </div>
                </div>

                {/* Approach 2 */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <svg className="w-8 h-8 text-blue-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-blue-900 mb-2">Fostering Equity</h3>
                    <p className="text-gray-600 mb-3">Ensuring equal access to quality healthcare services for all members of society.</p>
                    <a href="#" className="text-blue-900 hover:text-yellow-500 font-medium">See Details →</a>
                  </div>
                </div>

                {/* Approach 3 */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <svg className="w-8 h-8 text-blue-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364-.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-blue-900 mb-2">Catalyzing Innovation</h3>
                    <p className="text-gray-600 mb-3">Developing cutting-edge solutions and technologies to address healthcare challenges.</p>
                    <a href="#" className="text-blue-900 hover:text-yellow-500 font-medium">See Details →</a>
                  </div>
                </div>

                {/* Approach 4 */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <svg className="w-8 h-8 text-blue-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-blue-900 mb-2">Enterprises for Good</h3>
                    <p className="text-gray-600 mb-3">Creating sustainable business models that generate social impact and financial sustainability.</p>
                    <a href="#" className="text-blue-900 hover:text-yellow-500 font-medium">See Details →</a>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Neuroscience Hub */}
            <div className="relative">
              <div className="relative rounded-lg overflow-hidden">
                <Image
                  src="/neuroscience-hub.svg"
                  alt="Neuroscience & Psychiatry Hub"
                  width={600}
                  height={400}
                  className="w-full h-80 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-blue-900/80 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <h3 className="text-2xl font-bold mb-2">Neuroscience & Psychiatry Hub Ltd.</h3>
                  <p className="text-sm mb-4 opacity-90">
                    A dedicated center for mental health care offering comprehensive neuroscience and psychiatric services to improve mental wellness in our communities.
                  </p>
                  <button className="btn-primary">
                    Know More
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Story Section 1 */}
      <section className="section-padding bg-blue-50">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="order-2 lg:order-1">
              <div className="bg-white p-6 sm:p-8 rounded-lg shadow-lg">
                <h3 className="text-2xl sm:text-3xl font-bold text-blue-900 mb-4 leading-tight">
                  When 8-year-old Nur was diagnosed with leukaemia, his family struggled to afford treatment
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Until SAJIDA Hospital stepped in. Our comprehensive care program not only provided medical treatment but also supported the family through their difficult journey, ensuring Nur received the care he needed.
                </p>
                <button className="btn-secondary">
                  Read Nur's Story
                </button>
              </div>
            </div>
            <div className="order-1 lg:order-2">
              <Image
                src="/healthcare-story.svg"
                alt="Healthcare story"
                width={600}
                height={400}
                className="w-full h-64 sm:h-80 object-cover rounded-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Story Section 2 */}
      <section className="section-padding bg-blue-900 text-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div>
              <Image
                src="/annual-report.svg"
                alt="Annual Report"
                width={600}
                height={400}
                className="w-full h-64 sm:h-80 object-cover rounded-lg"
              />
            </div>
            <div>
              <div className="bg-yellow-400 text-blue-900 p-2 rounded font-bold text-sm inline-block mb-4">
                Annual Report
              </div>
              <h3 className="text-2xl sm:text-3xl font-bold mb-6 leading-tight">
                Change starts with each and every one of us!
              </h3>
              <button className="btn-primary">
                Download Report
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-blue-900 text-white section-padding">
        <div className="max-w-7xl mx-auto">
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            {/* Logo and Newsletter */}
            <div className="sm:col-span-2 lg:col-span-2">
              <div className="text-xl sm:text-2xl font-bold mb-4">SAJIDA Foundation</div>
              <p className="text-blue-200 mb-6 text-sm sm:text-base">
                Committed to improving health, happiness, and dignity for all through innovative healthcare solutions and community empowerment.
              </p>

              {/* Newsletter */}
              <div>
                <h4 className="font-semibold mb-3">Newsletter</h4>
                <div className="flex flex-col sm:flex-row gap-2">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 px-4 py-2 rounded-full sm:rounded-l-full sm:rounded-r-none text-blue-900 focus:outline-none"
                  />
                  <button className="bg-yellow-400 text-blue-900 px-6 py-2 rounded-full sm:rounded-l-none sm:rounded-r-full font-semibold hover:bg-yellow-300 transition-colors">
                    Subscribe
                  </button>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2 text-blue-200">
                <li><a href="#" className="hover:text-yellow-400 transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Annual Report</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">News & Events</a></li>
                <li><a href="#" className="hover:text-yellow-400 transition-colors">Procurement</a></li>
              </ul>
            </div>

            {/* Connect With Us */}
            <div>
              <h4 className="font-semibold mb-4">Connect With Us</h4>
              <div className="flex space-x-4 mb-6">
                <a href="#" className="text-blue-200 hover:text-yellow-400 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="#" className="text-blue-200 hover:text-yellow-400 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </a>
                <a href="#" className="text-blue-200 hover:text-yellow-400 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a href="#" className="text-blue-200 hover:text-yellow-400 transition-colors">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </a>
              </div>

              {/* Accreditation */}
              <div>
                <h5 className="font-semibold mb-3">Accreditation</h5>
                <div className="flex space-x-2">
                  <div className="w-12 h-12 bg-white rounded flex items-center justify-center">
                    <span className="text-blue-900 font-bold text-xs">ISO</span>
                  </div>
                  <div className="w-12 h-12 bg-white rounded flex items-center justify-center">
                    <span className="text-blue-900 font-bold text-xs">JCI</span>
                  </div>
                  <div className="w-12 h-12 bg-white rounded flex items-center justify-center">
                    <span className="text-blue-900 font-bold text-xs">WHO</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Copyright */}
          <div className="border-t border-blue-800 pt-6 text-center text-blue-200">
            <p>&copy; 2024 SAJIDA Foundation. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
