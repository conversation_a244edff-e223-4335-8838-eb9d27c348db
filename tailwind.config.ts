import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'sajida-deep-blue': '#1E3A5F',
        'sajida-bright-yellow': '#FFD200',
        'sajida-white': '#FFFFFF',
        'sajida-light-blue': '#EFF4FA',
        'sajida-overlay': 'rgba(30, 58, 95, 0.7)',
      },
      fontFamily: {
        'script': ['Dancing Script', 'cursive'],
        'body': ['Open Sans', 'sans-serif'],
      },
    },
  },
  plugins: [],
}

export default config
